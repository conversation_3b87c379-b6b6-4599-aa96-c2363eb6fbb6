import React from 'react';
import Head from 'next/head';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Instrument_Sans } from "next/font/google";
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import FeatureSection from '@/components/FeatureSection';
import HowItWorksSection from '@/components/HowItWorksSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import CTASection from '@/components/CTASection';
import TrendingMovies from '@/components/TrendingMovies';
import Footer from '@/components/Footer';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const InstrumentSans = Instrument_Sans({
  variable:"--font-instrument-sans",
  subsets:["latin"]
})

const instrumentSans = Instrument_Sans({
  variable: "--font-instrument-sans",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

export default function Home() {

  return (
    <>
      <Head>
        <title>TrueView</title>
        <meta name="description" content="Experience movies together with friends online. TrueView makes watching movies a social experience again." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="Truview-logo.jpg" />
      </Head>
      <div className={`${geistSans.variable} ${geistMono.variable} ${instrumentSans.variable} min-h-screen bg-black`}>
        <Header />
        <Hero />
        <TrendingMovies />
        <FeatureSection />
        <HowItWorksSection />
        <TestimonialsSection />
        <CTASection />
        <Footer />
      </div>
    </>
  );
}
