@import "tailwindcss";

*{
  margin:0;
}

:root {
  --background: #000000;
  --foreground: #ffffff;
  --purple-primary: #B84BFF;
  --purple-dark: #1A0B1F;
  --purpleText: #E950D5;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --purpleText:var(--purpleText)
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
}


.text-purple-500 {
  color: var(--purple-primary);
}

.bg-purple-600 {
  background-color: var(--purple-primary);
}

.bg-purple-700 {
  background-color: #9F3FE0;
}

.hover\:text-purple-300:hover {
  color: #D9A4FF;
}

.hover\:bg-purple-700:hover {
  background-color: #9F3FE0;
}


/* For Signup and Login Forms */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px transparent inset !important;
    -webkit-text-fill-color: var(--foreground) !important;
    transition: background-color 5000s ease-in-out 0s;
}