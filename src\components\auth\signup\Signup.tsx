import React from 'react'
import { usePasswordToggle } from '@/hooks/hooks'
import {SubmitHand<PERSON>, useForm} from "react-hook-form"
import { EyeClosed, EyeOpen } from '@/ui/ui'
import { FormType, GoogleSignUp, SignUpError } from '@/types'
import { InstrumentSans } from '@/pages'
import Truview from '../../TruviewComponent'
import { ToastContainer, toast } from 'react-toastify';
import Link from 'next/link'
import { signUpGoogle, signUpUser } from '@/services/auth'
import { GoogleLogin } from '@react-oauth/google';
import Cookies from "js-cookie"

const Signup = () => {
  const {register,handleSubmit,reset,setError,watch,formState:{errors,isSubmitting,isValid}} = useForm<FormType>()
  const [ passwordType, togglePasswordType] = usePasswordToggle()
  const [ passwordTypeSecond, togglePasswordTypeSecond] = usePasswordToggle()

  // Submit Form Data
  
  const onSubmit:SubmitHandler<FormType>= async (data)=>{
    try{
      // User data
      const userData = await signUpUser(data)
      Cookies.set("USER_INFO",JSON.stringify(userData),{
        expires: 3,
        secure: true,
        path: "/",
      })
      toast.dark("Form submitted successfully")
      reset() 
    } catch(error){
      const signUpError =  error as SignUpError
      if (signUpError.data){
        const {email,username,password,password2} = signUpError.data
        if (username){ 
          if (Array.isArray(username)) {
            setError("username",{
              message:username[0]
            })
          } else if (username.error) {
            setError("username",{
              message:username.error,
            })
          }
        } if (email){
          setError("email",{
            message:email.error
          })
        }else if (password){
          setError("password",{
            message:password[0]
          })
        } else if (password2){
          setError("password2",{
            message:password2[0]
          })
        }
      } else {
        toast.dark("An error occured")
      }
    }
  }

  //Email Validation
  const validEmail =(email:string)=>{
    const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
    return emailRegex.test(email)
  }

  // Google sign in
  const googleSign=async (data:string)=>{
    try{
      console.log(data)
      const userData:GoogleSignUp = {
          auth_token:data,
        }
      const userToken = await signUpGoogle(userData)
      Cookies.set("GOOGLE_SIGN_IN_INFO",userToken.data.access,{
        expires: 3,
        secure: true,
        path: "/",
      })

      toast.dark("Sign In Successful")
    }catch(error){
      toast.dark("An error occured")
      console.error(error)
    }
  }
  return (
    // background Image
    <div className=" text-white w-full bg-[url(/images/background.jpg)]  ">
      
      {/* Black shade */}
      <div className="bg-[#000000]/90" >
        <div className={`${InstrumentSans.className} min-h-screen flex justify-center items-center `}>
          <Truview/>

          {/* Reusable Toast */}
          <ToastContainer/>

          {/* Backdrop */}
          <div className='w-[90%] min-w-[300px] sm:w-fit border py-[1.45em] px-[1.06em] border-[#ffffff]/20 rounded-[50px] bg-[#000000]/10 backdrop-blur-[20px]'>

            {/* Main Component */}
            <main className='sm:py-[1.215em] sm:px-[2.438rem] flex flex-col gap-[1em] items-center'>

              <div className=' w-full flex flex-col justify-center items-center gap-2.5'>
                <h1 className='font-normal text-[#E3E3E3] text-[1.325rem] leading-none'>Create an account</h1>
                <p className='font-normal text-[#717171] text-[1rem] leading-none'>Already have an account?  
                  <Link href={"/(auth)/login"} className='text-(--purpleText) inline-block pl-1.5 hover:text-(--purpleText)/70'> Login</Link>
                </p>
              </div>

              {/* Sign up Form */}
              <div className='flex text-[20px] flex-col gap-[20px]'>
                <form onSubmit={handleSubmit(onSubmit)} className=' text-[10px] flex flex-col gap-[20px] '>

                  <div className='flex flex-col gap-[15px] w-[306px]'>

                    {/* Username */}
                    <div>
                      <input {...register("username",{
                        required:"Please enter your username"
                      })} className={`bg-[#29202D]/50 focus:bg-[#29202D] w-full ${errors.username?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} placeholder='Username' type="text"/>
                      {errors.username&&<p className="py-1 text-[#DD3838]">{errors.username.message}</p>}
                    </div>
                  
                    {/* Email */}
                    <div>
                      <input {...register("email",{
                        required:"Please enter a valid email address",
                        validate:(value)=>{
                         if (!validEmail(value)){
                           return "Invalid email address"
                          } else {
                            return true
                          }}
                        })} className={`bg-[#29202D]/50 w-full focus:bg-[#29202D] ${errors.email?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} type="email" placeholder='Email'/>
                      {errors.email && (
                        <div className="py-1 text-[#DD3838]">{errors.email.message}</div>)}
                    </div>

                    {/* Password */}
                    <div >
                      <div className='relative'>
                        <input {...register("password",{
                          required:"Password is required",
                          minLength:{
                            value:8,
                            message:"Password must have at least 8 characters"
                          }
                        })} className={`bg-[#29202D]/50 w-full  focus:bg-[#29202D] ${errors.password?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} type={passwordType} placeholder='Password'/>
                        <div onClick={togglePasswordType} className='absolute top-[25%] sm:top-[28%] left-[90%] sm:left-[92%]'>
                          {passwordType === "password"? <EyeOpen/> : <EyeClosed/>}
                        </div>
                      </div>
                      {errors.password && (
                        <div className="py-1 text-[#DD3838]">{errors.password.message}</div>)}
                    </div>

                    {/* Confirm Password */}
                    <div>
                      <div className='relative'>
                        <input {...register("password2",{
                          required:"Confirm password",
                          validate:(value:string)=>{
                            if (watch("password") !== value){
                              return "Passwords don't match"
                            }
                          }
                        })} className={`bg-[#29202D]/50 w-full  focus:bg-[#29202D] ${errors.password2?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} type={passwordTypeSecond} placeholder='Confirm Password'/>
                        <div onClick={togglePasswordTypeSecond} className='absolute top-[25%] sm:top-[28%] left-[90%] sm:left-[92%]'>
                          {passwordTypeSecond === "password" ? <EyeOpen/> : <EyeClosed/>}
                        </div>
                      </div>
                      {errors.password2 && (
                        <div className="py-1 text-[#DD3838]">{errors.password2.message}</div>
                      )}
                    </div>
                  </div>

                  {/* Form submit Button */}
                  <button disabled={isSubmitting} className={`py-3 rounded-[5px] text-[#ffffff] bg-[#2C142E] ${isValid?"bg-[#7E3A84]":""} hover:bg-[#7E3A84] `}>{isSubmitting?"Submitting":"Create my Account"}</button>

                </form>

                {/* Google Authentication Button */}
                <GoogleLogin
                  onSuccess={ async credentialResponse => {
                    if (credentialResponse.credential){
                      googleSign(credentialResponse.credential)
                    }
                  }}
                  onError={() => {
                    toast.dark("Sign In Failed")
                  }}
                />
              </div>
                    
              <div className='font-normal text-[10px]'>
                <p>By signing up, you have agreed to our <span className='text-(--purpleText)'>Terms and Conditions</span></p>
              </div>
            </main>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Signup