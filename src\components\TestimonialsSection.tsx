import React from 'react';
import Image from 'next/image';


interface Testimonial {
  id: string;
  name: string;
  comment: string;
  rating: number;
  imageSrc: string;
}

const testimonials: Testimonial[] = [
  {
    id: 'sarah-k',
    name: '<PERSON><PERSON>',
    comment: 'Felt like we were in the same room!',
    rating: 5,
    imageSrc: '/trending-1.jpg',
  },
  {
    id: 'samuel-m',
    name: '<PERSON>',
    comment: 'Perfect for long-distance date night.',
    rating: 5,
    imageSrc: '/trending-2.jpg',
  },
  {
    id: 'amina-l',
    name: '<PERSON><PERSON>',
    comment: 'Great for family movie weekend.',
    rating: 4,
    imageSrc: '/trending-3.jpg',
  },
];


const StarRating: React.FC<{ rating: number }> = ({ rating }) => {
  return (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <span key={i} className="text-yellow-400 text-lg mr-0.5">
          {i < rating ? '★' : '☆'}
        </span>
      ))}
    </div>
  );
};

const TestimonialsSection: React.FC = () => {
  return (
    <section className="py-16 relative" style={{ background: "linear-gradient(180deg, #391A3B 0%, #050710 94.24%)" }}>
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6">
        <div className="max-w-3xl mx-auto">
          <div className="mb-12">
            <h2 className="text-4xl md:text-5xl font-semibold text-white mb-4">
              Our Top 3 Testimonials
            </h2>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-white/70 text-sm">5 star rating badge</span>
              <div className="flex text-yellow-400">
                <span>★</span>
                <span>★</span>
                <span>★</span>
                <span>★</span>
                <span>★</span>
              </div>
              <span className="text-white/70 text-sm">4/5</span>
            </div>
          </div>
          <div className="space-y-12 mt-12">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="flex items-start border-b border-white/10 pb-12 last:border-0"
            >
              <div className="mr-8 flex-shrink-0">
                <div className="relative w-24 h-24 md:w-28 md:h-28 rounded-full overflow-hidden bg-purple-700 flex items-center justify-center">
                  <div className="absolute inset-0 flex items-center justify-center text-white text-2xl font-bold z-0">
                    {testimonial.name.charAt(0)}
                  </div>
                  <Image
                    src={testimonial.imageSrc}
                    alt={`${testimonial.name} profile`}
                    fill
                    className="object-cover z-10"
                    sizes="(max-width: 768px) 96px, 128px"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </div>
              </div>
              <div>
                <h3 className="text-xl font-medium text-white mb-1">
                  {testimonial.name}
                </h3>
                <p className="text-white/90 mb-2 text-base">
                  {testimonial.comment}
                </p>
                <StarRating rating={testimonial.rating} />
              </div>
            </div>
          ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
