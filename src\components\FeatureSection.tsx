import React from 'react';
import { Alarm<PERSON>lock, Video, RotateCcw } from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div style={{
      background: 'linear-gradient(180deg, #082769 0%, #7E3A84 100%)',
      backdropFilter: 'blur(25px)',
      boxShadow: '10px 15px 10px 0px #0000001A inset, 0px -10px 25px 0px #00000040 inset'
    }} className="rounded-lg p-8 flex flex-col items-center text-center h-full">
      <div className="text-white mb-6">
        {icon}
      </div>
      <h3 className="text-white text-lg font-medium mb-2">{title}</h3>
      <p className="text-white/90 text-sm leading-relaxed">{description}</p>
    </div>
  );
};

const FeatureSection: React.FC = () => {
  return (
    <section className="py-16 md:py-20 relative" style={{
      background: "linear-gradient(0deg, #7E3A84 1.83%, #4D2451 20.24%, #1D0D1E 100%)"
    }}>
      <div className="relative z-1">
      <div className="max-w-[1440px] mx-auto px-4 sm:px-6">

        <div className="text-center mb-10">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-normal text-white mb-2">
            Why You'll Love Watching Together,
          </h2>
          <p className="text-white/70 text-sm max-w-2xl mx-auto">
            Built for remote movie nights, powered by real connection
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-5">
          <FeatureCard
            icon={<AlarmClock size={36} color="white" strokeWidth={1.5} />}
            title="Perfectly Synced Viewing"
            description="Everyone watches in sync - no lag."
          />

          <FeatureCard
            icon={<Video size={36} color="white" strokeWidth={1.5} />}
            title="See and Hear Each Other"
            description="Video chat live while you watch together"
          />

          <FeatureCard
            icon={<RotateCcw size={36} color="white" strokeWidth={1.5} />}
            title="Replay Missed Moments"
            description="Request to rewind and replay scenes."
          />
        </div>
      </div>
      </div>
    </section>
  );
};

export default FeatureSection;
