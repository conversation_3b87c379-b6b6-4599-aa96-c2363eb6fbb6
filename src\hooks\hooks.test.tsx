import '@testing-library/jest-dom'
import { act , renderHook } from '@testing-library/react';
import { usePasswordToggle } from './hooks';

describe("usePasswordToggle", () => {
  it("should initialize with password type", () => {
    const { result } = renderHook(() => usePasswordToggle());
    
    expect(result.current[0]).toBe("password");
  });

  it("should toggle password type between text and password", () => {
    const { result } = renderHook(() => usePasswordToggle());
    
    expect(result.current[0]).toBe("password");
    
    act(() => {
      result.current[1](); 
    });
    expect(result.current[0]).toBe("text");
    
    act(() => {
      result.current[1]();
    });
    expect(result.current[0]).toBe("password");
  });
});
