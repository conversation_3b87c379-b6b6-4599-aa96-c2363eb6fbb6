import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Twitter, Instagram, Linkedin, MessageSquare, Globe } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-black text-white py-16 border-t border-white/10">
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6">
        <div className="flex flex-col lg:flex-row justify-between mb-16">
          <div className="mb-8 lg:mb-0 lg:ml-29">
            <div className="flex items-center mb-4">
              <Link href="/" className="flex items-center">
                <Image
                  src="/Truview-logo.jpg"
                  alt="TrueView Logo"
                  width={200}
                  height={50}
                  className="object-contain w-auto h-10"
                  priority
                />
              </Link>
            </div>
            <p className="text-white/60 mb-6">CineTogether</p>
            <div className="flex space-x-4">
              <Link
                href="https://twitter.com"
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-[#1DA1F2] hover:bg-[#1DA1F2] hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Twitter size={18} />
              </Link>
              <Link
                href="https://instagram.com"
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-[#E4405F] hover:bg-[#E4405F] hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Instagram size={18} />
              </Link>
              <Link
                href="https://linkedin.com"
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-[#0077B5] hover:bg-[#0077B5] hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Linkedin size={18} />
              </Link>
              <Link
                href="https://discord.com"
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-[#5865F2] hover:bg-[#5865F2] hover:text-white transition-all duration-300 hover:scale-110"
              >
                <MessageSquare size={18} />
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 lg:gap-24 lg:mr-25">
            <div>
              <h3 className="text-white font-medium mb-6">Product</h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/features" className="text-white/60 hover:text-white transition-colors">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="text-white/60 hover:text-white transition-colors">
                    How it Works
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-medium mb-6">Company</h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/about" className="text-white/60 hover:text-white transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="text-white/60 hover:text-white transition-colors">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="text-white/60 hover:text-white transition-colors">
                    Blog
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-medium mb-6">Support</h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/help" className="text-white/60 hover:text-white transition-colors">
                    Help Centered
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-white/60 hover:text-white transition-colors">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-white/60 hover:text-white transition-colors">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="pt-6 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div className="flex flex-col md:flex-row md:space-x-8 mb-4 md:mb-0">
              <Link href="/terms" className="text-white/60 hover:text-white transition-colors mb-2 md:mb-0">
                Terms of Service
              </Link>
              <Link href="/privacy" className="text-white/60 hover:text-white transition-colors">
                Privacy Policy
              </Link>
            </div>

            <div className="flex items-center">
              <Globe size={16} className="text-white/60 mr-2" />
              <span className="text-white/60">English</span>
            </div>
          </div>
          <div>
            <p className="text-white/60 text-sm">© 2024 CineTogether. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
