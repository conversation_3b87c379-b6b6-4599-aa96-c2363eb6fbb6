import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const CTASection: React.FC = () => {
  return (
    <section className="py-20 bg-black relative">
      <div className="max-w-[1200px] mx-auto px-4 sm:px-6">
        <div
          className="rounded-xl overflow-hidden relative"
        >
          <div className="absolute inset-0 z-0">
            <Image
              src="/purple.jfif"
              alt="CTA background"
              fill
              priority
              className="object-cover"
              style={{
                objectFit: 'cover',
              }}
              sizes="100vw"
              quality={100}
            />
            <div className="absolute inset-0 bg-purple-900/60 backdrop-blur-sm"></div>
          </div>

          <div className="relative z-10 py-14 px-8 text-center">
            <h2 className="text-2xl md:text-3xl lg:text-4xl text-white font-medium max-w-2xl mx-auto leading-relaxed">
              Create your room in Seconds and bring your favorite people closer - no matter the distance
            </h2>

            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-8 sm:gap-16 md:gap-30">
              <Link
                href="/start-watching"
                className="text-white py-3 px-8 rounded-lg text-center font-medium min-w-[300px]"
                style={{
                  background: 'linear-gradient(90deg, #7E3A84 0%, #533293 100%)'
                }}
              >
                Start a Watch Party
              </Link>

              <Link
                href="/features"
                className="text-white py-3 px-8 text-center rounded-lg font-medium border border-white/30 min-w-[300px] hover:bg-white/10 transition-colors"
              >
                Explore Features
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
