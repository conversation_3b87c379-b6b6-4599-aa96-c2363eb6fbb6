import '@testing-library/jest-dom'
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Login from './Login';

describe("Login component",()=>{
  beforeEach(()=>{render(<Login/>)})


  it("renders a header",()=>{
    const text = screen.getByText(/Welcome Back/i)
    expect(text).toBeInTheDocument()
  })


  it("render username and password inputs",()=>{
    expect(screen.getByPlaceholderText(/username/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/password/i)).toBeInTheDocument()
  })


  it("render submit buton",()=>{
    const submit = screen.getByRole("button")
    expect(submit.textContent).toMatch("Login")
  })


  it ("show validation errors when form is submitted empty",async ()=>{
    fireEvent.click(screen.getByRole("button", { name:"Login"} ))

    await waitFor(()=>{
      expect(screen.getByText(/Please enter your username/i)).toBeInTheDocument()
      expect(screen.getByText(/Password is required/i)).toBeInTheDocument()
    })
  })



  it('toggles password visibility', async () => {
    
    const passwordInput = screen.getByPlaceholderText('Password') as HTMLInputElement;
    const toggleButton = screen.getByPlaceholderText('Password').nextElementSibling;
    
    // Initially should be password type
    expect(passwordInput.type).toBe('password');
    
    // Click to show password
    fireEvent.click(toggleButton!);
    expect(passwordInput.type).toBe('text');
    
    // Click again to hide password
    fireEvent.click(toggleButton!);
    expect(passwordInput.type).toBe('password');
  });
})
