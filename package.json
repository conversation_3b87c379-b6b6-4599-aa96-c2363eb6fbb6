{"name": "truview-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watchAll"}, "dependencies": {"@react-oauth/google": "^0.12.2", "axios": "^1.9.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "20.17.32", "@types/react": "19.1.2", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "5.8.3"}}