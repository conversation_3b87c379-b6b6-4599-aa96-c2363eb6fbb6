// Google Icon
export const GoogleIcon=()=>{
  return(
    <svg width="15" height="15" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.2539 1.25434C7.45626 2.22486 5.04359 4.06695 3.37025 6.51003C1.69692 8.95311 0.851129 11.8684 0.957115 14.8277C1.0631 17.787 2.11528 20.6343 3.9591 22.9514C5.80292 25.2685 8.34119 26.9333 11.2011 27.7012C13.5197 28.2995 15.9489 28.3258 18.2798 27.7778C20.3915 27.3035 22.3437 26.2889 23.9455 24.8334C25.6125 23.2723 26.8225 21.2863 27.4455 19.089C28.1225 16.6996 28.243 14.1867 27.7976 11.7434H14.7776V17.1443H22.318C22.1673 18.0058 21.8443 18.8279 21.3685 19.5616C20.8926 20.2953 20.2736 20.9254 19.5486 21.4143C18.6278 22.0234 17.5899 22.4332 16.5014 22.6175C15.4097 22.8205 14.29 22.8205 13.1983 22.6175C12.0918 22.3887 11.0451 21.932 10.1248 21.2765C8.64641 20.23 7.53631 18.7432 6.95296 17.0284C6.35974 15.2815 6.35974 13.3875 6.95296 11.6406C7.36821 10.4161 8.05466 9.30114 8.96109 8.37903C9.99838 7.30441 11.3116 6.53627 12.7567 6.15888C14.2019 5.78149 15.723 5.80944 17.1533 6.23965C18.2706 6.58263 19.2923 7.18189 20.137 7.98965C20.9872 7.14382 21.836 6.2958 22.6833 5.44559C23.1208 4.9884 23.5976 4.55309 24.0286 4.08496C22.7392 2.88507 21.2257 1.9514 19.5748 1.33746C16.5686 0.245878 13.2792 0.216543 10.2539 1.25434Z" fill="white"/>
      <path d="M10.2538 1.2543C13.2788 0.215802 16.5682 0.244365 19.5748 1.33524C21.2259 1.95335 22.7387 2.89151 24.0263 4.09587C23.5888 4.56399 23.1273 5.00149 22.681 5.45649C21.8323 6.30378 20.9842 7.14816 20.137 7.98961C19.2922 7.18185 18.2705 6.5826 17.1532 6.23961C15.7234 5.80789 14.2023 5.77833 12.7568 6.15418C11.3113 6.53002 9.99726 7.29675 8.95883 8.37024C8.0524 9.29235 7.36595 10.4073 6.9507 11.6318L2.41602 8.12086C4.03916 4.90209 6.84953 2.43998 10.2538 1.2543Z" fill="#E33629"/>
      <path d="M1.21314 11.599C1.45687 10.391 1.86152 9.22122 2.41626 8.12085L6.95095 11.6405C6.35773 13.3875 6.35773 15.2814 6.95095 17.0284C5.44012 18.195 3.92856 19.3675 2.41626 20.5459C1.02753 17.7815 0.603991 14.6319 1.21314 11.599Z" fill="#F8BD00"/>
      <path d="M14.7778 11.7412H27.7978C28.2432 14.1845 28.1227 16.6974 27.4456 19.0868C26.8227 21.2841 25.6127 23.2701 23.9456 24.8312C22.4822 23.6893 21.0122 22.5562 19.5488 21.4143C20.2743 20.9249 20.8936 20.2941 21.3694 19.5596C21.8453 18.8251 22.168 18.0021 22.3181 17.14H14.7778C14.7756 15.3418 14.7778 13.5415 14.7778 11.7412Z" fill="#587DBD"/>
      <path d="M2.41406 20.5459C3.92635 19.3792 5.43792 18.2067 6.94875 17.0284C7.53327 18.7438 8.64495 20.2306 10.125 21.2765C11.0481 21.929 12.0971 22.3819 13.205 22.6065C14.2967 22.8095 15.4164 22.8095 16.5081 22.6065C17.5966 22.4223 18.6346 22.0124 19.5553 21.4034C21.0188 22.5453 22.4888 23.6784 23.9522 24.8203C22.3507 26.2765 20.3984 27.2919 18.2866 27.7668C15.9556 28.3148 13.5264 28.2885 11.2078 27.6903C9.37404 27.2006 7.66117 26.3375 6.17656 25.1549C4.6052 23.9074 3.32178 22.3352 2.41406 20.5459Z" fill="#319F43"/>
    </svg>
  )
}

// Truview New Logo
export const TruviewLogo =()=>{
  return (
    <svg width="40" height="35" viewBox="0 0 40 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_63_103)">
      <path fillRule="evenodd" clipRule="evenodd" d="M29.0944 11.5224C31.4243 11.5224 33.3119 13.4014 33.3119 15.7183C33.3119 18.0363 31.4232 19.9142 29.0944 19.9142C26.7656 19.9142 24.877 18.0352 24.877 15.7183C24.877 13.4004 26.7656 11.5224 29.0944 11.5224ZM26.2589 35H13.7408C13.4692 35 13.2465 34.7785 13.2465 34.5083C13.2465 34.238 13.4692 34.0165 13.7408 34.0165H26.2589C26.5304 34.0165 26.7531 34.238 26.7531 34.5083C26.7531 34.7785 26.5304 35 26.2589 35ZM7.36413 0H32.6365C36.6864 0 40.0007 3.2973 40.0007 7.32652V24.6692C40.0007 28.6984 36.6864 31.9957 32.6365 31.9957H7.36413C3.31422 31.9957 0 28.6984 0 24.6692V7.32652C0 3.2973 3.31422 0 7.36413 0ZM7.94165 0.98453H32.059C35.8602 0.98453 38.9695 4.07892 38.9695 7.85968V24.136C38.9695 27.9178 35.8592 31.0112 32.059 31.0112H7.94165C4.14044 31.0112 1.03121 27.9168 1.03121 24.136V7.85968C1.03121 4.07788 4.14148 0.98453 7.94165 0.98453ZM20.7272 23.4144C15.4546 20.6896 8.34956 18.2806 5.61285 24.8162C7.54311 15.4026 16.7355 16.6801 20.1549 18.3489C20.1476 18.3054 32.0673 25.603 35.6303 17.0963C34.0194 24.9528 26.2963 26.2924 20.7272 23.4144ZM14.0488 7.85657C16.3776 7.85657 18.2662 9.73557 18.2662 12.0525C18.2662 14.3694 16.3776 16.2484 14.0488 16.2484C11.7189 16.2484 9.83133 14.3694 9.83133 12.0525C9.83133 9.73453 11.72 7.85657 14.0488 7.85657ZM29.0955 12.507C30.854 12.507 32.3244 13.9698 32.3244 15.7183C32.3244 17.4679 30.854 18.9307 29.0955 18.9307C27.3369 18.9307 25.8666 17.4679 25.8666 15.7183C25.8666 13.9687 27.3369 12.507 29.0955 12.507Z" fill="white" fillOpacity="0.7"/>
      </g>
      <defs>
      <clipPath id="clip0_63_103">
      <rect width="40" height="35" fill="white"/>
      </clipPath>
      </defs>
    </svg>
  )
}

// Eye closed password 
export const EyeClosed=()=>{
  return(
    <svg width="15" height="15" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.73314 4.40937C12.0625 4.13177 14.4187 4.62418 16.442 5.81141C18.4653 6.99864 20.0444 8.81544 20.9381 10.9844C21.0215 11.2089 21.0215 11.4559 20.9381 11.6804C20.5701 12.5711 20.0844 13.4085 19.4941 14.1704M13.0841 13.4914C12.5183 14.0378 11.7605 14.3402 10.9739 14.3334C10.1873 14.3266 9.4349 14.011 8.87868 13.4548C8.32245 12.8986 8.00695 12.1462 8.00011 11.3596C7.99328 10.573 8.29566 9.81517 8.84214 9.24937" stroke="#8E8E8E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M16.479 16.8324C15.1525 17.6181 13.6725 18.1094 12.1394 18.2728C10.6063 18.4361 9.05597 18.2678 7.59365 17.7793C6.13133 17.2907 4.79121 16.4933 3.66423 15.4411C2.53725 14.389 1.64977 13.1067 1.06202 11.6814C0.978677 11.4569 0.978677 11.2099 1.06202 10.9854C1.94865 8.83523 3.50869 7.03062 5.50802 5.84237M1.00002 1.33337L21 21.3334" stroke="#8E8E8E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}

export const EyeOpen =()=>{
  return(
    <svg width="15" height="15" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.06202 12.348C1.97868 12.1235 1.97868 11.8765 2.06202 11.652C2.87372 9.68385 4.25153 8.00103 6.02079 6.81689C7.79004 5.63275 9.87106 5.00061 12 5.00061C14.129 5.00061 16.21 5.63275 17.9792 6.81689C19.7485 8.00103 21.1263 9.68385 21.938 11.652C22.0214 11.8765 22.0214 12.1235 21.938 12.348C21.1263 14.3161 19.7485 15.999 17.9792 17.1831C16.21 18.3672 14.129 18.9994 12 18.9994C9.87106 18.9994 7.79004 18.3672 6.02079 17.1831C4.25153 15.999 2.87372 14.3161 2.06202 12.348Z" stroke="#8E8E8E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="#8E8E8E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}















