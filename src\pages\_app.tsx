import "@/styles/globals.css";
import type { AppProps } from "next/app";
import { GoogleOAuthProvider } from '@react-oauth/google';


export default function App({ Component, pageProps }: AppProps) {
  const clientId = process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID

  return (
    <GoogleOAuthProvider clientId={clientId ? clientId : ""}>
      <Component {...pageProps} />
    </GoogleOAuthProvider>
  )
  
}
