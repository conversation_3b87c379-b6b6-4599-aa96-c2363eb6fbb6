import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const Hero: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 z-0">
        <Image
          src="/Background-image.jpg"
          alt="Movie theater background"
          fill
          priority
          className="object-cover"
          style={{
            objectFit: 'cover',
            filter: 'brightness(0.35)'
          }}
          sizes="100vw"
          quality={100}
        />
      </div>
      <div className="relative z-1 max-w-[1440px] mx-auto px-6 text-center pt-16">
        <div className="flex flex-col items-center justify-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-normal text-white leading-tight mb-4">
            Movies hit different when you<br />
            watch with <span className="font-bold italic">friends</span>
          </h1>

          <div className="mt-12">
            <Link
              href="/start-watching"
              className="bg-transparent hover:bg-white/10 text-white border border-white px-10 py-3 font-medium text-center transition-colors inline-block"
            >
              Start watching
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
