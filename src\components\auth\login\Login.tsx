import React from 'react'
import { usePasswordToggle } from '@/hooks/hooks'
import {SubmitHandler, useForm} from "react-hook-form"
import { EyeClosed, EyeOpen } from '@/ui/ui'
import { GoogleSignUp, LoginType } from '@/types'
import { InstrumentSans } from '@/pages'
import Truview from '../../TruviewComponent'
import { ToastContainer, toast } from 'react-toastify';
import Link from 'next/link'
import { GoogleLogin } from '@react-oauth/google';
import { signUpGoogle } from '@/services/auth';
import Cookies from 'js-cookie';

const Login = () => {
  const {register,handleSubmit,reset,formState:{errors,isSubmitting,isValid}} = useForm<LoginType>()
  const [ passwordType, togglePasswordType] = usePasswordToggle()

  // Submit Form Data
  const onSubmit:SubmitHandler<LoginType>= async (data)=>{
    try{
      await new Promise((resolve)=>setTimeout(resolve,1000))

      // User data
      console.log(data)
      toast.dark("Login successful")
      reset() 
    } catch(error){
      toast.dark("An error occured while logging in")
    }
  }

  // Google sign in
  const googleSign=async (data:string)=>{
    try{
      const userData:GoogleSignUp = {
          auth_token:data,
        }
      const userToken = await signUpGoogle(userData)
      Cookies.set("GOOGLE_SIGN_IN_INFO",userToken.data.access,{
        expires: 3,
        secure: true,
        path: "/",
      })

      toast.dark("Sign In Successful")
    }catch(error){
      toast.dark("An error occured")
      console.log(error)
    }
  }

  return (
    // background Image
    <div className=" w-full bg-[url(/images/background.jpg)]  ">
      
      {/* Black shade */}
      <div className="bg-[#000000]/90" >

        <div className={`${InstrumentSans.className} min-h-screen flex justify-center items-center `}>
          <Truview/>

          {/* Reusable Toast */}
          <ToastContainer/>

          {/* Backdrop */}
          <div className='w-[90%] min-w-[300px] sm:w-fit border py-[1.45em] px-[1.06em] border-[#ffffff]/20 rounded-[50px] bg-[#000000]/10 backdrop-blur-[20px]'>

            {/* Main Component */}
            <main className='sm:py-[1.215em] sm:px-[2.438rem] flex flex-col gap-[1em] items-center'>
              <div className=' w-full flex flex-col justify-center items-center gap-2.5'>
                <h1 className='font-normal text-[#E3E3E3] text-[1.325rem] leading-none'>Welcome Back</h1>
                <p className='font-normal text-[#717171] text-[1rem] leading-none'>New to Truview?  
                  <Link href={"/(auth)/signup"} className='text-(--purpleText) inline-block pl-1.5'> Create an Account</Link></p>
              </div>

              {/* Sign up Form */}
              <div className='flex text-[20px] flex-col gap-[20px]'>
                <form onSubmit={handleSubmit(onSubmit)} className=' text-[10px] flex flex-col gap-[20px] '>

                  <div className='flex flex-col gap-[15px] w-[306px]'>

                    {/* Username */}
                    <div className='w-full'>
                      <input {...register("username",{
                        required:"Please enter your username"
                      })} className={`bg-[#29202D]/50 w-full focus:bg-[#29202D] ${errors.username?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} placeholder='Username' type="text" name="username"/>
                      {errors.username&&<p className="py-1 text-[#DD3838]">{errors.username.message}</p>}
                    </div>

                    {/* Password */}
                    <div className='w-full' >
                      <div className='relative w-full'>
                        <input {...register("password",{
                          required:"Password is required",
                          minLength:{
                            value:8,
                            message:"Password must have at least 8 characters"
                          }
                        })} className={`bg-[#29202D]/50 w-full focus:bg-[#29202D] ${errors.password?"border-1 border-[#DD3838]":""} p-2.5 rounded-[5px]`} type={passwordType} placeholder='Password'/>
                        <div onClick={togglePasswordType} className='absolute top-[25%] sm:top-[28%] left-[90%] sm:left-[92%]'>
                          {passwordType === "password"? <EyeOpen/> : <EyeClosed/>}
                        </div>
                      </div>
                      {errors.password && (
                        <div className="py-1 text-[#DD3838]">{errors.password.message}</div>)}
                    </div>
                      
                    {/* Forgot password */}
                    <div className='flex self-end text-(--purpleText)'>
                      <p>Forgot password?</p>
                    </div>
                  </div>
                      
                  {/* Form submit Button */}
                  <button disabled={isSubmitting} className={`py-3 rounded-[5px] text-[#ffffff] bg-[#2C142E] ${isValid?"bg-[#7E3A84]":""} hover:bg-[#7E3A84] `}>{isSubmitting?"Logging in..":"Login"}</button>
                      
                </form>
                {/* Google Authentication Button */}
                <GoogleLogin
                  onSuccess={ async credentialResponse => {
                    if (credentialResponse.credential){
                      googleSign(credentialResponse.credential)
                    }
                  }}
                  onError={() => {
                    toast.dark("Sign In Failed")
                  }}
                />     
              </div>      
                
              <div className='font-normal text-[10px]'>
                <p>By signing up, you have agreed to our <span className='text-(--purpleText)'>Terms and Conditions</span></p>
              </div>
            </main>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login