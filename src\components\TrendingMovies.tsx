import React, { useState, useEffect, useRef, TouchEvent } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface Movie {
  id: string;
  posterSrc: string;
  watchUrl: string;
}
const trendingMovies: Movie[] = [
  {
    id: 'blade-runner',
    posterSrc: '/trending-1.jpg',
    watchUrl: '/watch/blade-runner',
  },
  {
    id: 'harry-potter',
    posterSrc: '/trending-2.jpg',
    watchUrl: '/watch/harry-potter',
  },
  {
    id: 'guardians-of-galaxy',
    posterSrc: '/trending-3.jpg',
    watchUrl: '/watch/guardians-of-galaxy',
  },
  {
    id: 'inception',
    posterSrc: '/movies/inception.jpg',
    watchUrl: '/watch/inception',
  },
  {
    id: 'interstellar',
    posterSrc: '/movies/interstellar.jpg',
    watchUrl: '/watch/interstellar',
  },
  {
    id: 'the-dark-knight',
    posterSrc: '/movies/the-dark-knight.jpg',
    watchUrl: '/watch/the-dark-knight',
  },
  {
    id: 'pulp-fiction',
    posterSrc: '/movies/pulp-fiction.jpg',
    watchUrl: '/watch/pulp-fiction',
  },
  {
    id: 'the-matrix',
    posterSrc: '/movies/the-matrix.jpg',
    watchUrl: '/watch/the-matrix',
  },
  {
    id: 'avatar',
    posterSrc: '/movies/avatar.jpg',
    watchUrl: '/watch/avatar',
  }
];

const TrendingMovies: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visibleCount, setVisibleCount] = useState(3);
  const touchStartX = useRef<number | null>(null);
  const touchEndX = useRef<number | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setVisibleCount(1);
      } else if (window.innerWidth < 1024) {
        setVisibleCount(2);
      } else {
        setVisibleCount(3);
      }
    };

    handleResize();

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? Math.max(0, trendingMovies.length - visibleCount) : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= trendingMovies.length - visibleCount ? 0 : prevIndex + 1
    );
  };

  const handleTouchStart = (e: TouchEvent<HTMLDivElement>) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;

    const diff = touchStartX.current - touchEndX.current;
    const threshold = 50;

    if (diff > threshold) {
      handleNext();
    } else if (diff < -threshold) {
      handlePrev();
    }

    touchStartX.current = null;
    touchEndX.current = null;
  };
  const visibleMovies = trendingMovies.slice(currentIndex, currentIndex + visibleCount);

  return (
    <section className="py-6 md:py-12 lg:py-16 bg-black">
      <div className="max-w-[1440px] mx-auto px-4 sm:px-6">
        <div className="mb-6 md:mb-12 text-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-normal text-white mb-1 md:mb-2">Trending today</h2>
          <p className="text-white/80 text-sm sm:text-base md:text-xl">Pick a movie. Have fun</p>
        </div>

        <div className="relative">
          <div
            ref={carouselRef}
            className="sm:static"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <button
              onClick={handlePrev}
              className="hidden sm:flex absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 md:-translate-x-6 z-10 bg-black/30 hover:bg-black/50 rounded-full w-10 h-10 md:w-12 md:h-12 items-center justify-center cursor-pointer transition-all"
              aria-label="Previous movies"
              style={{ border: '1px solid rgba(255, 255, 255, 0.2)' }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 19L8 12L15 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-6 md:gap-8">
              {visibleMovies.map((movie) => (
                <div key={movie.id} className="flex flex-col items-center">
                  <div className="relative aspect-[2/3] mb-6 w-full max-w-[240px] sm:max-w-[280px] md:max-w-[300px] mx-auto">
                    <Image
                      src={movie.posterSrc}
                      alt={`Movie: ${movie.id.split('-').join(' ')}`}
                      fill
                      className="object-cover rounded-lg shadow-lg"
                      sizes="(max-width: 640px) 80vw, (max-width: 1024px) 40vw, 30vw"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100 transition-opacity rounded-lg"></div>
                  </div>

                  <Link
                    href={movie.watchUrl}
                    className="text-white py-2 sm:py-3 px-4 rounded text-center font-medium w-full max-w-[200px] relative group"
                    style={{
                      background: 'linear-gradient(90deg, #7E3A84 0%, #533293 100%)'
                    }}
                  >
                    <span
                      className="absolute inset-0 rounded opacity-0 group-hover:opacity-100 group-active:opacity-100 transition-opacity duration-300 ease-out group-active:duration-0"
                      style={{
                        background: 'linear-gradient(90deg, #7E3A84 0%, #533293 100%)',
                        opacity: 0.8
                      }}
                    ></span>
                    <span className="relative z-10">Watch with friends</span>
                  </Link>
                </div>
              ))}
            </div>
            <button
              onClick={handleNext}
              className="hidden sm:flex absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 md:translate-x-6 z-10 bg-black/30 hover:bg-black/50 rounded-full w-10 h-10 md:w-12 md:h-12 items-center justify-center cursor-pointer transition-all"
              aria-label="Next movies"
              style={{ border: '1px solid rgba(255, 255, 255, 0.2)' }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 5L16 12L9 19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>


          <div className="flex justify-center items-center mt-6 sm:hidden">
            <button
              onClick={handlePrev}
              className="mx-2 p-2 text-white"
              aria-label="Previous movie"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 19L8 12L15 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <div className="flex items-center">
              {Array.from({ length: trendingMovies.length }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 mx-1 rounded-full transition-all ${
                    index === currentIndex ? 'bg-purple-600 scale-125' : 'bg-white/30'
                  }`}
                  aria-label={`Go to movie ${index + 1}`}
                />
              ))}
            </div>

            <button
              onClick={handleNext}
              className="mx-2 p-2 text-white"
              aria-label="Next movie"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 5L16 12L9 19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrendingMovies;
