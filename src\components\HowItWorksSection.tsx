import React from 'react';
import Image from 'next/image';

const HowItWorksSection: React.FC = () => {
  return (
    <section className="py-16 relative" style={{ backgroundColor: "#000000", paddingBottom: "100px" }}>

      <div className="relative z-10 max-w-[1200px] mx-auto px-4 sm:px-6">

        <div className="text-center mb-16">
          <h2
            className="mx-auto"
            style={{
              width: "573px",
              maxWidth: "100%",
              height: "36px",
              fontFamily: "var(--font-instrument-sans), sans-serif",
              fontSize: "48px",
              lineHeight: "36px",
              fontWeight: 600,
              background: "linear-gradient(90deg, #082769 0%, #E6D132 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              color: "transparent",
              display: "inline-block"
            }}
          >
            How it works
          </h2>
        </div>


        <div className="flex flex-col md:flex-row">

          <div className="w-full md:w-3/5 md:pr-8">

            <div className="relative mb-10" style={{ marginTop: "-20px" }}>
              <div className="relative" style={{
                maxWidth: "100%",
                margin: "0 auto"
              }}>

                <Image
                  src="/MacBook Air (15 inch).png"
                  alt="MacBook Air"
                  width={250}
                  height={170}
                  className="w-full h-auto"
                  style={{
                    objectFit: "contain",
                    maxWidth: "100%"
                  }}
                  priority
                />
              </div>
            </div>


            <div className="mb-16">
              <div className="flex items-start">
                <div className="text-white font-medium mr-3" style={{ minWidth: "20px" }}>1.</div>
                <div>
                  <h3 className="text-white text-xl font-semibold mb-3">Create a Room</h3>
                  <ul className="text-white text-sm space-y-2" style={{ listStyleType: "none", opacity: 0.9 }}>
                    <li>• Just "Start a Watch Party" on the home page</li>
                    <li>• Choose between a private room (for you and your friends), or a public room</li>
                    <li>• Set a room name and password</li>
                    <li>• Set your name or nickname (or pick the default – either way, it only takes seconds to setup)</li>
                  </ul>
                </div>
              </div>
            </div>


            <div className="mb-16">
              <div className="flex items-start">
                <div className="text-white font-medium mr-3 text-xl" style={{ minWidth: "20px" }}>3.</div>
                <div>
                  <h3 className="text-white text-xl font-semibold mb-3">Pick a Movie</h3>
                  <ul className="text-white text-sm space-y-2" style={{ listStyleType: "none", opacity: 0.9 }}>
                    <li>• Choose from specially selected content, or sync an external movie</li>
                    <li>• The host selects what to play, but everyone watches in perfect sync</li>
                    <li>• It's like going to the movies – but better</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>


          <div className="w-full md:w-2/5 md:pl-8 md:pt-32">

            <div className="mb-20">
              <div className="flex items-start">
                <div className="text-white font-medium mr-3 text-xl" style={{ minWidth: "20px" }}>2.</div>
                <div>
                  <h3 className="text-white text-xl font-semibold mb-3">Invite Your People</h3>
                  <ul className="text-white text-sm space-y-2" style={{ listStyleType: "none", opacity: 0.9 }}>
                    <li>• Share the room link with friends and family via text or social media</li>
                    <li>• They don't need an account to join, just a web browser</li>
                    <li>• Everyone enters the virtual room together to watch together</li>
                  </ul>
                </div>
              </div>
            </div>


            <div className="mb-16">
              <div className="flex items-start">
                <div className="text-white font-medium mr-3 text-xl" style={{ minWidth: "20px" }}>4.</div>
                <div>
                  <h3 className="text-white text-lg font-medium mb-2">Control the Playback</h3>
                  <ul className="text-white text-sm space-y-2" style={{ listStyleType: "none", opacity: 0.9 }}>
                    <li>• As the host, you can play, stop, or skip to any scene anytime</li>
                    <li>• Everyone's video will pause and resume in perfect sync</li>
                    <li>• No lagging when someone pauses – it keeps things smooth and enjoyable</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>


        <div className="text-white/50 text-xs text-center mt-8 max-w-4xl mx-auto px-4">
          By using TrueView's watch-together features, you agree that you are responsible for the content you share with others. TrueView does not have the rights to or control over the content you watch. You must be the authorized user and have the rights to the content you share.
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
