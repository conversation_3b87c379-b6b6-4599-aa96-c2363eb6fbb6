import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const Header: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="w-full absolute top-0 z-10">
      <div className="max-w-[1440px] mx-auto px-4 sm:px-6 py-4 sm:py-5 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/" className="flex items-center">
            <Image
              src="/Truview-logo.jpg"
              alt="TrueView Logo"
              width={2050}
              height={50}
              className="object-contain w-auto h-6 sm:h-8 md:h-auto"
              priority
            />
          </Link>
        </div>
        <button
          className="sm:hidden text-white p-2 focus:outline-none"
          onClick={toggleMobileMenu}
          aria-label="Toggle mobile menu"
        >
          {mobileMenuOpen ? (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          ) : (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 6H20M4 12H20M4 18H20" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          )}
        </button>


        <nav className="hidden sm:flex items-center space-x-4 md:space-x-8">
          <Link href="/" className="text-white hover:text-purple-300 font-medium text-sm md:text-base">
            Home
          </Link>
          <Link href="/contact" className="text-white hover:text-purple-300 font-medium text-sm md:text-base">
            Contact
          </Link>
          <Link href="/faq" className="text-white hover:text-purple-300 font-medium text-sm md:text-base">
            FAQ
          </Link>
          <Link
            href={"/(auth)/signup"}
            style={{
              background: '#7E3A84',
              width: '76px',
              height: '34px',
              borderRadius: '8px',
              padding: '7px 10px',
              gap: '10px',
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            className="text-white font-medium text-sm transition-colors ml-2 md:ml-4"
          >
            Sign up
          </Link>
        </nav>


        {mobileMenuOpen && (
          <div className="fixed inset-0 bg-black/95 z-50 sm:hidden flex flex-col items-center justify-center">
            <button
              className="absolute top-4 right-4 text-white p-2"
              onClick={toggleMobileMenu}
              aria-label="Close mobile menu"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <nav className="flex flex-col items-center space-y-6">
              <Link
                href="/"
                className="text-white hover:text-purple-300 font-medium text-xl"
                onClick={toggleMobileMenu}
              >
                Home
              </Link>
              <Link
                href="/contact"
                className="text-white hover:text-purple-300 font-medium text-xl"
                onClick={toggleMobileMenu}
              >
                Contact
              </Link>
              <Link
                href="/faq"
                className="text-white hover:text-purple-300 font-medium text-xl"
                onClick={toggleMobileMenu}
              >
                FAQ
              </Link>
              <Link
                href="/(auth)/signup"
                style={{
                  background: '#7E3A84',
                  width: '76px',
                  height: '34px',
                  borderRadius: '8px',
                  padding: '7px 10px',
                  gap: '10px',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                className="text-white font-medium text-sm mt-4"
                onClick={toggleMobileMenu}
              >
                Sign up
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
