import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// Define the movie data structure
export interface Movie {
  id: string;
  title: string;
  posterSrc: string;
  watchUrl: string;
}

interface MovieCarouselProps {
  title: string;
  subtitle?: string;
  movies: Movie[];
}

const MovieCarousel: React.FC<MovieCarouselProps> = ({ title, subtitle, movies }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Function to handle navigation
  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? Math.max(0, movies.length - 3) : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= movies.length - 3 ? 0 : prevIndex + 1
    );
  };

  // Get the movies to display based on current index
  const visibleMovies = movies.slice(currentIndex, currentIndex + 3);

  return (
    <section className="py-12 bg-black">
      <div className="max-w-[1440px] mx-auto px-6">
        {/* Heading */}
        <div className="mb-12 text-center">
          <h2 className="text-4xl font-normal text-white mb-2">{title}</h2>
          {subtitle && <p className="text-white/80 text-xl">{subtitle}</p>}
        </div>

        {/* Movie Carousel */}
        <div className="relative">
          {/* Previous Button */}
          <button
            onClick={handlePrev}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-6 z-10 bg-black/30 hover:bg-black/50 rounded-full w-12 h-12 flex items-center justify-center cursor-pointer transition-all"
            aria-label="Previous movies"
            style={{ border: '1px solid rgba(255, 255, 255, 0.2)' }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 19L8 12L15 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>

          {/* Movies Grid */}
          <div className="grid grid-cols-3 gap-8">
            {visibleMovies.map((movie) => (
              <div key={movie.id} className="flex flex-col items-center">
                <div className="relative aspect-[2/3] mb-4 w-full max-w-[300px]">
                  <Image
                    src={movie.posterSrc}
                    alt={movie.title}
                    fill
                    className="object-cover rounded-lg"
                    sizes="(max-width: 768px) 100vw, 33vw"
                    priority
                  />
                </div>
                <Link
                  href={movie.watchUrl}
                  className="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded text-center font-medium transition-colors"
                  style={{ width: '180px', margin: '0 auto' }}
                >
                  Watch with friends
                </Link>
              </div>
            ))}
          </div>

          {/* Next Button */}
          <button
            onClick={handleNext}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-6 z-10 bg-black/30 hover:bg-black/50 rounded-full w-12 h-12 flex items-center justify-center cursor-pointer transition-all"
            aria-label="Next movies"
            style={{ border: '1px solid rgba(255, 255, 255, 0.2)' }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 5L16 12L9 19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default MovieCarousel;
