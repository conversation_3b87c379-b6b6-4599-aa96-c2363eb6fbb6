import '@testing-library/jest-dom'
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Signup from './Signup';
import { signUpUser } from '@/services/auth';


  jest.mock("@/services/auth",()=>({
    signUpUser:jest.fn().mockResolvedValue({data:{success:true}})
  }))


describe("Signup component",()=>{
  beforeEach(()=>{render(<Signup/>)})


  it("renders a header",()=>{
    const text = screen.getByText(/Create an account/i)
    expect(text).toBeInTheDocument()
  })


  it("render username, email and password inputs",()=>{
    expect(screen.getByPlaceholderText(/username/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument()
    const passwords = screen.getAllByPlaceholderText(/password/i)
    passwords.forEach((item)=>{
      expect(item).toBeInTheDocument()
    })
  })


  it("render submit buton",()=>{
    const submit = screen.getByRole("button")
    expect(submit.textContent).toMatch("Create my Account")
  })


  it ("show validation errors when form is submitted empty",async ()=>{
    fireEvent.click(screen.getByRole("button", { name:"Create my Account"} ))

    await waitFor(()=>{
      expect(screen.getByText(/Please enter your username/i)).toBeInTheDocument()
      expect(screen.getByText(/Please enter a valid email address/i)).toBeInTheDocument()
      expect(screen.getByText(/Password is required/i)).toBeInTheDocument()
      expect((screen.getByText(/Confirm password/i))).toBeInTheDocument()
    })
  })


  it("submits the form successfully with valid data", async ()=>{
 
    // Fill out the form
    fireEvent.change(screen.getByPlaceholderText("Username"),{target:{value:"testuser"}})
    fireEvent.change(screen.getByPlaceholderText("Email"),{target:{value:"<EMAIL>"}})
    fireEvent.change(screen.getByPlaceholderText("Password"),{target:{value:"password123"}})
    fireEvent.change(screen.getByPlaceholderText("Confirm Password"),{target:{value:"password123"}})

    // Submit the form
    fireEvent.click(screen.getByRole("button"),{name:"Create an Account"})

    await waitFor(()=>{
      expect(signUpUser).toHaveBeenCalledWith({
        username:"testuser",
        email:"<EMAIL>",
        password:"password123",
        password2:"password123",
      })
    })
  })

  it('toggles password visibility', async () => {
    
    const passwordInput = screen.getByPlaceholderText('Password') as HTMLInputElement;
    const toggleButton = screen.getByPlaceholderText('Password').nextElementSibling;
    
    // Initially should be password type
    expect(passwordInput.type).toBe('password');
    
    // Click to show password
    fireEvent.click(toggleButton!);
    expect(passwordInput.type).toBe('text');
    
    // Click again to hide password
    fireEvent.click(toggleButton!);
    expect(passwordInput.type).toBe('password');
  });
})
