
// Sign up type
export type FormType = {
  username:string,
  email:string,
  password:string,
  password2:string,
}

// login type
export type LoginType = {
  username:string,
  password:string
}

export type SignUpResponse = {
  status: 'success' | 'failure';
  message?: string;
  data: {
    token: string;
    username: string;
    email: string;
  } | {
    field?: {
      error?: string;
    } | string[];
  };
  error_message?: string;
  error_code?: string;
}

export type SignUpError ={
  status: "failure",
  error_message: "Invalid Entry",
  error_code: "invalid_entry",
  data: {
      email?:{error:string},
      username?:{error:string} | [string],
      password:[string]
      password2:[string]
  }
}

export type GoogleSignUp={
  auth_token:string,
}

export type GoogleSignInResponse = {
  status: string,
  message: string,
  data: {
    access: string,
    refresh: string,
  }
}