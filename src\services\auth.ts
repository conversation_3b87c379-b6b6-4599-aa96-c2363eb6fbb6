import axios from "axios";
import { instance } from "./axios";
import { FormType, GoogleSignInResponse, GoogleSignUp, SignUpError, SignUpResponse } from "@/types";


export const signUpUser = async (userData:FormType):Promise<SignUpResponse>=>{
  try {
    const response = await instance.post("/user/sign-up/",userData);
    return response.data;
  } catch (error){

    if (axios.isAxiosError(error)){
      const responseError = error.response?.data as SignUpError;
      throw responseError;
    } else {
      throw new Error("An unexpected error occured")
    }
  }
}

export const signUpGoogle = async (token:GoogleSignUp):Promise<GoogleSignInResponse>=>{
  try{
    const response = await instance.post("/user/google/",token)
    return response.data;
  } catch (error){
    if (axios.isAxiosError(error)){
      const responseError = error.response?.data
      throw responseError;
    } else {
      throw new Error("An unexpected error occured")
    }
  }
}